/* eslint-disable import/prefer-default-export */
import { releaseListApi } from './releaseListApi';
import { sysApi } from './sysApi';
import { opApi } from './opApi';
import { serviceApi } from './serviceApi';
import { userApi } from './userApi';
import { communityApi } from './communityApi';
import { creatorApi } from './creatorApi';
import { reportApi } from './reportApi';
import { searchApi } from './searchApi';
import { findApi } from './find';
import { creativeRevenueApi } from './creativeRevenue';

export const listApi = {
  getReleaseList: releaseListApi.getReleaseList,
  getFocusList: releaseListApi.getFocusList,
  getChannelRecommendsList: releaseListApi.getRecommendsList,
  getUserDeletedList: releaseListApi.getUserDeletedList,
  getAdminList: sysApi.getAdminList,
  getChannelList: sysApi.getChannelList,
  getAppNavList: sysApi.getAppNavList,
  getCityNavList: sysApi.getCityNavList,
  getCenterNavList: sysApi.getCenterNavList,
  getInterfaceList: sysApi.getInterfaceList,
  getPermissionGroupList: sysApi.getPermissionGroupList,
  getPermissionGroupUserList: sysApi.getGroupUsersList,
  getAppVersionList: sysApi.getAppVersionList,
  getSensitiveWords: opApi.getSensitiveWords,
  getStartPageList: opApi.getStartPageList,
  getFingerPaperList: opApi.getFingerPaperList,
  getReadPaperList: opApi.getReadPaperList,
  getPushNotifyList: opApi.getPushNotifyList,
  getServiceFocusList: serviceApi.getServiceFocusList,
  getAppServiceList: serviceApi.getAppServiceList,
  getServiceCategoryList: serviceApi.getServiceCategoryList,
  getAvatarList: userApi.getAvatarList,
  getUserList: userApi.getUserList,
  getWaistcoatUserList: userApi.getWaistcoatUserList,
  getPrivilegeGroupList: userApi.getPrivilegeGroupList,
  getPGUserList: userApi.getPGUserList,
  getUserMessageList: userApi.getUserMessageList,
  getRecommendUserList: userApi.getRecommendUserList,
  getBatchCreditList: userApi.getBatchCreditList,
  getWhiteHostList: opApi.getWhiteHostList,
  getUserComplaintList: releaseListApi.getUserComplaintList,
  getTopArticlesList: releaseListApi.getTopArticlesList,
  getProposalList: opApi.getProposalList,
  getFeedbackList: userApi.getFeedbackList,
  getFAQList: userApi.getFAQList,
  getTopicList: opApi.getTopicList,
  getTopicAuditList: opApi.getTopicAuditList,
  getRecommendTopicList: opApi.getRecommendTopicList,
  getHotRecommendTopicList: opApi.getHotRecommendTopicList,
  getTopicArticleList: opApi.getTopicArticleList,
  getNewsTopicArticleList: opApi.getNewsTopicArticleList,
  getOrgUserList: userApi.getOrgUserList,
  getVirtualUserList: userApi.getVirtualUserList,
  getWhiteList: userApi.getWhiteList,
  getImportantArticlesList: releaseListApi.getImportantArticlesList,
  getOrgUserArticleList: userApi.getOrgUserArticleList,
  getListArticleRecommendList: releaseListApi.getListArticleRecommendList,
  getCommentRecommendList: releaseListApi.getCommentRecommendList,
  getAppCrashList: sysApi.getAppCrashList,
  getTopicCategoryList: opApi.getTopicCategoryList,
  getStickerList: opApi.getStickerList,
  getRedPacketList: opApi.getRedPacketList,
  getMusicList: opApi.getMusicList,
  getMusicCategoryList: opApi.getMusicCategoryList,
  getRepacketDataList: opApi.getRepacketDataList,
  getUserMoneyList: opApi.getUserMoneyList,
  getUserMoneyDetail: opApi.getUserMoneyDetail,
  getPacketTotalList: opApi.getPacketTotalList,
  getHotNewsList: opApi.getHotNewsList,
  getOrgCategoryList: userApi.getOrgCategoryList,
  getSearchKeywordList: serviceApi.getSearchKeywordList,
  getSearchKeywordCategoryList: serviceApi.getSearchKeywordCategoryList,
  getChaokeStarList: userApi.getChaokeStarList,
  getActivityList: opApi.getActivityList,
  getLiveTopList: releaseListApi.getLiveTopList,
  getLiveRecommendList: releaseListApi.getLiveRecommendList,
  getLiveRecommendArticleList: releaseListApi.getLiveRecommendArticleList,
  getOrgRankList: userApi.getOrgRankList,
  getHotWordList: opApi.getHotWordList,
  getDisclosureList: userApi.getDisclosureList,
  getRankDetailList: userApi.getRankDetailList,
  getRankLog: userApi.getRankLog,
  getReleaseOperateRecommendList: releaseListApi.getReleaseOperateRecommendList,
  getCreditLevelList: userApi.getCreditLevelList,
  getMedalList: opApi.getMedalList,
  getSignList: opApi.getSignList,
  getSummaryRecommentList: releaseListApi.getSummaryRecommentList,
  getAdList: releaseListApi.getAdList,
  getArSceneList: opApi.getArSceneList,
  getThemeList: opApi.getThemeList,
  getThemeRemindList: opApi.getThemeRemindList,
  getUserImageAuditList: userApi.getUserImageAuditList,
  getVideoCarouselRecommendList: releaseListApi.getVideoCarouselRecommendList,
  getTopicRecommendList: releaseListApi.getTopicRecommendList,
  getMovieRedeemCodeInfo: opApi.getMovieRedeemCodeInfo,
  getMovieRedeemCodeList: opApi.getMovieRedeemCodeList,
  getMovieActivityRuleList: opApi.getMovieActivityRuleList,
  getMovieTicketActivityList: opApi.getMovieTicketActivityList,
  getMovieCouponUserList: opApi.getMovieCouponUserList,
  getListCommentPreset: userApi.getListCommentPreset,
  getListCommentTips: userApi.getListCommentTips,
  getColumnList: opApi.getColumnList,
  getServiceRecommendList: serviceApi.getServiceRecommendList,
  getProposalWordList: sysApi.getProposalWordList,
  getRecommendsceneScenelist: sysApi.getRecommendsceneScenelist,
  getRecommendsceneDimensionlist: sysApi.getRecommendsceneDimensionlist,
  getAppadvertpageList: opApi.getAppadvertpageList,
  getNavRecommendlList: opApi.getNavRecommendlList,
  getNavIconList: opApi.getNavIconList,
  // 专题模板
  getSubjectTemplateConfig: sysApi.getSubjectTemplateConfig,
  // 操作日志
  getOperationLogList: sysApi.getOperationLogList,
  // 广告列表
  getCommercialAdList: opApi.getCommercialAdList,
  // 广告编码列表
  getCommercialAdCodeList: opApi.getCommercialAdCodeList,
  // 置顶话题列表
  getTopManuscriptList: opApi.getTopManuscriptList,
  // 潮鸣号置顶新闻
  getTmhToppingList: releaseListApi.getTmhToppingList,
  // 长视频列表
  getLongVideoList: opApi.getLongVideoList,
  // 长视频用户列表
  getLongVideoUserList: opApi.getLongVideoUserList,
  // 稿件置频道的后的数据查询接口
  getToChannelList: releaseListApi.getToChannelList,
  // 阅读偏好列表查询接口
  getHobbyList: sysApi.getHobbyList,
  getShortURLList: sysApi.getShortURLList,
  recommendBannerList: communityApi.recommendBannerList,
  recommendFollowList: communityApi.recommendFollowList,
  //用户户内容推荐-话题列表
  recommendTopicLabelList: communityApi.recommendTopicLabelList,
  //用户户内容推荐-话题列表--new
  recommendTopicLabelListByPage: communityApi.recommendTopicLabelListByPage,

  // UGC标签列表管理
  getUGCTagList: opApi.getUGCTagList,
  // UGC标签稿件列表
  getUGCTagArticleList: opApi.getUGCTagArticleList,
  // App配置项配置列表信息
  getAppConfigList: sysApi.getAppConfigList,
  // 潮鸣号审核列表查询接口
  getAuditList: userApi.getAuditList,
  // 圈子列表
  getCircleList: communityApi.getCircleList,
  // 圈子内容列表
  getCircleArticleList: communityApi.getCircleArticleList,
  getCircleRecommendList: communityApi.getCircleRecommendList,
  // 账号权限列表
  getUGCPermissionList: communityApi.getUGCPermissionList,
  // 助推卡列表
  getUGCCardList: sysApi.getUGCCardList,
  // 版块列表
  getCircleBoardList: communityApi.getCircleBoardList,
  // 审核长视频列表申请列表
  getLongVideoDurationList: sysApi.getLongVideoDurationList,
  // 助推内容列表
  getArticleList: sysApi.getArticleList,

  // 频道浮窗-列表查询接口
  getChannelFloatWindowList: opApi.getChannelFloatWindowList,
  getSpiderBindList: userApi.getSpiderBindList,

  // 创作者
  // 公告列表
  creatorNoticeList: creatorApi.creatorNoticeList,
  // 图片配置列表
  reviewImageConfigList: creatorApi.reviewImageConfigList,

  // 绑定列表
  getPublishRankingList: opApi.getPublishRankingList,
  getSourceRankingList: opApi.getSourceRankingList,

  getAudioPlazaArticleList: opApi.getAudioPlazaArticleList,

  getGPTQuestionList: opApi.getGPTQuestionList,
  getHudongAvatarList: opApi.getHudongAvatarList,
  // 报料列表
  getMaterialsList: reportApi.getMaterialsList,
  // 跟进列表
  getFollowList: reportApi.getFollowList,
  // 推荐列表
  getrecommendMatList: reportApi.getRecommendMatList,
  // 记者/专家列表
  getReportUserList: reportApi.getReportUserList,
  getReportHomeConfigList: reportApi.getReportHomeConfigList,
  getReportRecommendDataList: reportApi.getReportRecommendDataList,

  // 圈子主理人列表
  getCircleOwnerList: communityApi.getCircleOwnerList,

  // 获取潮鸣号分类账号列表
  getOrgClassAccountList: userApi.getOrgClassAccountList,

  getReportFieldList: reportApi.getReportFieldList,
  getReportFieldPermList: reportApi.getReportFieldPermList,
  getBottomRecommendList: opApi.getBottomRecommendList,

  getCmhRankList: userApi.getCmhRankList,
  getSingleCmhRankList: userApi.getSingleCmhRankList,
  getSingleCmhArticleList: userApi.getSingleCmhArticleList,
  getCkRankList: userApi.getCkRankList,
  getSingleCkRankList: userApi.getSingleCkRankList,
  getCkRankBlacklist: userApi.getCkRankBlacklist,
  getSubjectRecommendList: opApi.getSubjectRecommendList,
  getCmhProvinceList: userApi.getCmhProvinceList,
  getCmhIndustryList: userApi.getCmhIndustryList,
  getBookRankList: opApi.getBookRankList,
  getColumnGroupsList: communityApi.getColumnGroupsList,
  getAiFeedbackList: opApi.getAiFeedbackList,
  searchUGCTopic: searchApi.searchUGCTopic,

  getCooperationAccountList: userApi.getCooperationAccountList,
  getCooperationCaseList: userApi.getCooperationCaseList,
  // 创作中心轮播图
  getCreativeCenterBannerList: opApi.getCreativeCenterBannerList,
  // 创作中心创作指南
  getCreativeCenterGuideList: opApi.getCreativeCenterGuideList,
  getAIChannelList: sysApi.getAIChannelList,
  // 抽屉列表
  getDrawerList: opApi.getDrawerList,
  getColumnClassList: opApi.getColumnClassList,
  getColumnClassPGList: opApi.getColumnClassPGList,
  getIMList: opApi.getIMList,
  getIMWhiteList: opApi.getIMWhiteList,
  getIMBlackList: opApi.getIMBlackList,
  getIMComplainList: opApi.getIMComplainList,
  // 潮圈首页
  getCircleHotList: opApi.getCircleHotList,
  getCircleBannerList: opApi.getCircleBannerList,
  getCircleHomeRecommendList: opApi.getCircleHomeRecommendList,
  
  // 影视首页管理
  // 重点推荐列表
  getKeyRecommendList: findApi.getKeyRecommendList,
  // 推荐位列表
  getRecommendPositionList: findApi.getRecommendPositionList,
  // 热门影视列表
  getHotFilmList: findApi.getHotFilmList,
  // 影视管理列表
  getMediaList: findApi.getMediaList,
  // 影视资源管理列表
  getMediaItemList: findApi.getMediaItemList,
  // 圈子活跃用户列表
  getCircleActiveRankList: communityApi.getCircleActiveRankList,

  getCircleClassPGList: communityApi.getCircleClassPGList,
  
  // 创意营收 - 稿费评级列表
  getRatedList: creativeRevenueApi.getRatedList,

  // 创作收益 - 提现申请已通过列表
  withdrawPassedList: creativeRevenueApi.withdrawPassedList,
  // 创作收益 - 提现申请待审核（一审）列表
  withdrawPendingList: creativeRevenueApi.withdrawPendingList,
  // 创作收益 - 提现申请待审核（二审）列表
  withdrawPendingFirstPassedList: creativeRevenueApi.withdrawPendingFirstPassedList,
  // 创作收益 - 提现申请审核未通过列表
  withdrawNotPassedList: creativeRevenueApi.withdrawNotPassedList,
  // 浙商人物-获取浙商人物列表
  getZheShangPersonList: opApi.getZheShangPersonList,
};
